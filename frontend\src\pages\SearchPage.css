.search-page {
  min-height: calc(100vh - 100px);
  padding: 3rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.search-page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.search-header {
  text-align: center;
  margin-bottom: 4rem;
  animation: fadeInUp 0.8s ease-out;
}

.search-header h2 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-family: var(--font-display);
  font-weight: 700;
  letter-spacing: -0.02em;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.search-header p {
  font-size: 1.25rem;
  color: var(--text-secondary);
  max-width: 650px;
  margin: 0 auto;
  font-weight: 400;
  line-height: 1.6;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-message {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  padding: 4rem;
  margin-top: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.welcome-content h3 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
  font-family: var(--font-display);
  font-weight: 600;
}

.welcome-content p {
  font-size: 1.25rem;
  margin-bottom: 2.5rem;
  color: var(--text-secondary);
  line-height: 1.7;
}

.search-tips {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
  border-radius: 20px;
  padding: 2rem;
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
  color: white;
  box-shadow: 0 15px 35px rgba(99, 102, 241, 0.2);
}

.search-tips h4 {
  margin-bottom: 1.5rem;
  color: white;
  font-size: 1.25rem;
  font-family: var(--font-display);
  font-weight: 600;
}

.search-tips ul {
  margin: 0;
  padding-left: 1.5rem;
  list-style: none;
}

.search-tips li {
  margin-bottom: 0.75rem;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.6;
  position: relative;
  padding-left: 1.5rem;
}

.search-tips li::before {
  content: "✨";
  position: absolute;
  left: 0;
  top: 0;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-page {
    padding: 1rem 0;
  }
  
  .search-page-container {
    padding: 0 0.5rem;
  }
  
  .search-header {
    margin-bottom: 2rem;
  }
  
  .search-header h2 {
    font-size: 2rem;
  }
  
  .search-header p {
    font-size: 1rem;
  }
  
  .welcome-message {
    padding: 2rem 1rem;
    margin-top: 2rem;
  }
  
  .search-tips {
    padding: 1rem;
  }
}
