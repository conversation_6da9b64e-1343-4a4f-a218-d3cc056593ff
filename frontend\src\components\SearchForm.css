.search-form-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  animation: fadeInUp 0.8s ease-out 0.1s both;
}

.search-form {
  max-width: 800px;
  margin: 0 auto;
}

.search-input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.search-input {
  flex: 1;
  padding: 1.25rem 1.5rem;
  font-size: 1.125rem;
  font-family: var(--font-primary);
  border: 2px solid rgba(99, 102, 241, 0.2);
  border-radius: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

.search-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.15);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

.search-button {
  padding: 1.25rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 700;
  font-family: var(--font-primary);
  border-radius: 16px;
  white-space: nowrap;
  min-width: 140px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.3);
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.search-button:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.filter-controls {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.filter-toggle {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}

.filter-toggle:hover {
  background: var(--border-light);
  transform: none;
  box-shadow: none;
}

.clear-filters {
  background: var(--error-color);
  color: white;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: var(--radius-md);
}

.filters {
  background: var(--bg-tertiary);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
  margin-top: 1rem;
  border: 1px solid var(--border-light);
}

.filter-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.filter-input {
  padding: 0.75rem;
  font-size: 0.875rem;
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
}

.checkbox-group {
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  margin-bottom: 0;
}

.filter-checkbox {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
}

.checkmark {
  position: relative;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .search-form-container {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  .search-input-group {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .search-button {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }
  
  .filter-controls {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
  }
  
  .filter-row {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .filters {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .search-form-container {
    padding: 1rem;
  }
  
  .search-input {
    font-size: 1rem;
    padding: 0.875rem;
  }
  
  .search-button {
    font-size: 0.875rem;
    padding: 0.875rem 1rem;
  }
}
