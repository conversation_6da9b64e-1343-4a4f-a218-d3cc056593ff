<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
</head>
<body>
    <h1>API Test Page</h1>
    <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
    <button onclick="testSearchEndpoint()">Test Search Endpoint</button>
    <div id="results"></div>

    <script>
        const BACKEND_URL = 'https://ieee-me-mishraji-ka-aatank-backend.onrender.com';
        
        async function testHealthEndpoint() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing health endpoint...</p>';
            
            try {
                const response = await fetch(`${BACKEND_URL}/health`);
                const data = await response.json();
                resultsDiv.innerHTML = `
                    <h3>Health Endpoint Result:</h3>
                    <p>Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h3>Health Endpoint Error:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }
        
        async function testSearchEndpoint() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<p>Testing search endpoint...</p>';
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/search?query=technology&page=1&limit=5`);
                const data = await response.json();
                resultsDiv.innerHTML = `
                    <h3>Search Endpoint Result:</h3>
                    <p>Status: ${response.status}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <h3>Search Endpoint Error:</h3>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
