# Dependencies
node_modules
.pnp
.pnp.js

# Production build
/dist
/build

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor and OS files# Editor and OS files




















/coverage# Test coverage.eslintcache.temp.cache# Cache*.sw?*.sln*.njsproj*.ntvs**.suo.DS_Store.idea!.vscode/settings.json!.vscode/extensions.json.vscode/*.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Cache
.cache
.temp
.eslintcache

# Test coverage
/coverage
